#include "usart_app.h"
#include "stdlib.h"
#include "stdarg.h"
#include "string.h"
#include "stdio.h"
#include "usart.h"
#include "mydefine.h"
#include "dac_app.h"

dac_waveform_t current_waveform_uart;
extern uint8_t wave_analysis_flag; // ��adc_app.c����
extern uint8_t wave_query_type;	   // ��adc_app.c����
uint16_t uart_rx_index = 0;
uint32_t uart_rx_ticks = 0;
uint8_t uart_rx_buffer[128] = {0};
uint8_t uart_rx_dma_buffer[128] = {0};
uint8_t uart_dma_buffer[128] = {0};
uint8_t uart_flag = 0;
struct rt_ringbuffer uart_ringbuffer;
uint8_t ringbuffer_pool[128];

int my_printf(UART_HandleTypeDef *huart, const char *format, ...)
{
	char buffer[512];
	va_list arg;
	int len;
	va_start(arg, format);
	len = vsnprintf(buffer, sizeof(buffer), format, arg);
	va_end(arg);
	HAL_UART_Transmit(huart, (uint8_t *)buffer, (uint16_t)len, 0xFF);
	return len;
}

void HAL_UART_RxCpltCallback(UART_HandleTypeDef *huart)
{
	if (huart->Instance == USART1)
	{
		uart_rx_ticks = uwTick;
		uart_rx_index++;
		HAL_UART_Receive_IT(&huart1, &uart_rx_buffer[uart_rx_index], 1);
	}
}

void HAL_UARTEx_RxEventCallback(UART_HandleTypeDef *huart, uint16_t Size)
{
	if (huart->Instance == USART1)
	{
		HAL_UART_DMAStop(huart);

		rt_ringbuffer_put(&uart_ringbuffer, uart_rx_dma_buffer, Size);

		memset(uart_rx_dma_buffer, 0, sizeof(uart_rx_dma_buffer));

		HAL_UARTEx_ReceiveToIdle_DMA(&huart1, uart_rx_dma_buffer, sizeof(uart_rx_dma_buffer));

		__HAL_DMA_DISABLE_IT(&hdma_usart1_rx, DMA_IT_HT);
	}
}

void parse_uart_command(uint8_t *buffer, uint16_t length)
{
	if (length < sizeof(uart_dma_buffer))
		buffer[length] = '\0';
	else
		buffer[sizeof(uart_dma_buffer) - 1] = '\0';

	if (strncmp((char *)buffer, "RTC Config ", 11) == 0)
	{
		// 提取时间参数（跳过"RTC Config "前缀）
		char *time_str = (char *)buffer + 11;
		rtc_config_result_t result = rtc_config_set_time(time_str);

		if (result == RTC_CONFIG_OK)
		{
			my_printf(&huart1, "RTC Config success\r\n");
			// 显示设置后的时间
			rtc_current_time_display();
		}
	}
	// RTC当前时间显示命令
	if (strncmp((char *)buffer, "RTC now", 7) == 0)
	{
		rtc_current_time_display();
	}
}

void uart_task(void)
{
	uint16_t length;

	length = rt_ringbuffer_data_len(&uart_ringbuffer);

	if (length == 0)
		return;

	rt_ringbuffer_get(&uart_ringbuffer, uart_dma_buffer, length);

	// 处理接收到的串口命令
	if (length > 0)
	{
		parse_uart_command(uart_dma_buffer, length);
	}

	// 清空缓冲区
	memset(uart_dma_buffer, 0, sizeof(uart_dma_buffer));
}
