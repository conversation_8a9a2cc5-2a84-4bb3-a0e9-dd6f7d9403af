#include "rtc_config.h"
#include <string.h>
#include <stdlib.h>
#include <ctype.h>

// 外部变量声明
extern RTC_HandleTypeDef hrtc;
extern UART_HandleTypeDef huart1;

// 外部函数声明
extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

/**
 * @file    rtc_config.c
 * @brief   RTC时间配置模块实现
 * @details 提供RTC时间设置和显示功能，支持灵活的时间格式解析
 *          采用低耦合设计，重用现有HAL库接口
 * <AUTHOR> Studio
 * @date    2025-01-15
 */

// 静态函数声明
static rtc_config_result_t parse_time_string(const char* time_str, 
                                            uint16_t* year, uint8_t* month, uint8_t* day,
                                            uint8_t* hour, uint8_t* minute, uint8_t* second);
static int is_valid_date(uint16_t year, uint8_t month, uint8_t day);
static int extract_numbers(const char* str, uint16_t* numbers, int max_count);

/**
 * @brief 解析时间字符串
 * @details 支持多种时间格式：YYYY MM DD HH MM SS（分隔符可变）
 *          支持的分隔符：空格、-、:、/等非数字字符
 *          年份支持2位和4位格式（2位年份自动加2000）
 * @param  time_str 时间字符串
 * @param  year 解析出的年份（输出参数）
 * @param  month 解析出的月份（输出参数）
 * @param  day 解析出的日期（输出参数）
 * @param  hour 解析出的小时（输出参数）
 * @param  minute 解析出的分钟（输出参数）
 * @param  second 解析出的秒钟（输出参数）
 * @retval rtc_config_result_t 解析结果
 */
static rtc_config_result_t parse_time_string(const char* time_str, 
                                            uint16_t* year, uint8_t* month, uint8_t* day,
                                            uint8_t* hour, uint8_t* minute, uint8_t* second)
{
    uint16_t numbers[6];
    int count;
    
    if (time_str == NULL)
    {
        return RTC_CONFIG_PARSE_ERROR;
    }
    
    // 提取所有数字
    count = extract_numbers(time_str, numbers, 6);
    
    // 检查是否提取到6个数字
    if (count != 6)
    {
        return RTC_CONFIG_PARSE_ERROR;
    }
    
    // 解析年份（支持2位和4位）
    if (numbers[0] < 100)
    {
        *year = numbers[0];  // 2位年份，RTC使用相对于2000年的值
    }
    else if (numbers[0] >= 2000 && numbers[0] <= 2099)
    {
        *year = numbers[0] - 2000;  // 4位年份，转换为相对于2000年的值
    }
    else
    {
        return RTC_CONFIG_PARSE_ERROR;
    }
    
    // 解析月、日、时、分、秒
    *month = (uint8_t)numbers[1];
    *day = (uint8_t)numbers[2];
    *hour = (uint8_t)numbers[3];
    *minute = (uint8_t)numbers[4];
    *second = (uint8_t)numbers[5];
    
    // 验证时间有效性
    if (!is_valid_date(*year + 2000, *month, *day) ||
        *hour > 23 || *minute > 59 || *second > 59)
    {
        return RTC_CONFIG_PARSE_ERROR;
    }
    
    return RTC_CONFIG_OK;
}

/**
 * @brief 从字符串中提取数字
 * @details 跳过所有非数字字符，提取连续的数字序列
 * @param  str 输入字符串
 * @param  numbers 输出数字数组
 * @param  max_count 最大数字个数
 * @retval int 实际提取的数字个数
 */
static int extract_numbers(const char* str, uint16_t* numbers, int max_count)
{
    int count = 0;
    int i = 0;
    int num_start = -1;
    
    while (str[i] != '\0' && count < max_count)
    {
        if (isdigit(str[i]))
        {
            if (num_start == -1)
            {
                num_start = i;  // 数字开始位置
            }
        }
        else
        {
            if (num_start != -1)
            {
                // 提取数字
                char num_str[16];
                int len = i - num_start;
                if (len < 16)
                {
                    strncpy(num_str, &str[num_start], len);
                    num_str[len] = '\0';
                    numbers[count++] = (uint16_t)atoi(num_str);
                }
                num_start = -1;
            }
        }
        i++;
    }
    
    // 处理字符串末尾的数字
    if (num_start != -1 && count < max_count)
    {
        char num_str[16];
        int len = i - num_start;
        if (len < 16)
        {
            strncpy(num_str, &str[num_start], len);
            num_str[len] = '\0';
            numbers[count++] = (uint16_t)atoi(num_str);
        }
    }
    
    return count;
}

/**
 * @brief 验证日期有效性
 * @details 检查年月日是否为有效日期，考虑闰年
 * @param  year 年份（完整年份，如2025）
 * @param  month 月份（1-12）
 * @param  day 日期（1-31）
 * @retval int 1表示有效，0表示无效
 */
static int is_valid_date(uint16_t year, uint8_t month, uint8_t day)
{
    // 检查月份
    if (month < 1 || month > 12)
    {
        return 0;
    }
    
    // 检查日期
    if (day < 1 || day > 31)
    {
        return 0;
    }
    
    // 每月天数
    uint8_t days_in_month[] = {31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31};
    
    // 检查闰年
    if (month == 2)
    {
        int is_leap = (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
        if (is_leap && day <= 29)
        {
            return 1;
        }
        else if (!is_leap && day <= 28)
        {
            return 1;
        }
        else
        {
            return 0;
        }
    }
    
    // 检查其他月份
    return day <= days_in_month[month - 1];
}

/**
 * @brief RTC时间设置函数
 * @details 解析时间字符串并设置RTC时间和日期
 *          支持多种时间格式：YYYY MM DD HH MM SS（分隔符可变）
 *          支持的分隔符：空格、-、:、/等
 * @param  time_str 时间字符串，格式如"2025 01 01 12 00 30"或"2025-01-01 12:00:30"
 * @retval rtc_config_result_t 设置结果（RTC_CONFIG_OK: 成功, 其他: 失败）
 */
rtc_config_result_t rtc_config_set_time(const char* time_str)
{
    uint16_t year;
    uint8_t month, day, hour, minute, second;
    RTC_TimeTypeDef sTime;
    RTC_DateTypeDef sDate;
    rtc_config_result_t parse_result;
    
    // 解析时间字符串
    parse_result = parse_time_string(time_str, &year, &month, &day, &hour, &minute, &second);
    if (parse_result != RTC_CONFIG_OK)
    {
        return parse_result;
    }
    
    // 构造时间结构
    sTime.Hours = hour;
    sTime.Minutes = minute;
    sTime.Seconds = second;
    sTime.DayLightSaving = RTC_DAYLIGHTSAVING_NONE;
    sTime.StoreOperation = RTC_STOREOPERATION_RESET;
    
    // 构造日期结构
    sDate.Year = year;  // 相对于2000年的年份
    sDate.Month = month;
    sDate.Date = day;
    sDate.WeekDay = RTC_WEEKDAY_MONDAY;  // 简化处理，固定为周一
    
    // 设置RTC时间
    if (HAL_RTC_SetTime(&hrtc, &sTime, RTC_FORMAT_BIN) != HAL_OK)
    {
        return RTC_CONFIG_SET_ERROR;
    }
    
    // 设置RTC日期
    if (HAL_RTC_SetDate(&hrtc, &sDate, RTC_FORMAT_BIN) != HAL_OK)
    {
        return RTC_CONFIG_SET_ERROR;
    }
    
    return RTC_CONFIG_OK;
}

/**
 * @brief RTC当前时间显示函数
 * @details 获取并显示当前RTC时间，格式为：Current Time: YYYY-MM-DD HH:MM:SS
 *          使用HAL库接口获取时间和日期信息，通过串口输出
 * @param  None
 * @retval None
 */
void rtc_current_time_display(void)
{
    RTC_TimeTypeDef sTime;
    RTC_DateTypeDef sDate;

    // 获取当前时间（必须先调用GetTime再调用GetDate以确保时间一致性）
    if (HAL_RTC_GetTime(&hrtc, &sTime, RTC_FORMAT_BIN) == HAL_OK)
    {
        if (HAL_RTC_GetDate(&hrtc, &sDate, RTC_FORMAT_BIN) == HAL_OK)
        {
            // 按指定格式输出时间：Current Time: YYYY-MM-DD HH:MM:SS
            // 注意：sDate.Year是相对于2000年的值，需要加2000得到完整年份
            my_printf(&huart1, "Current Time: %04d-%02d-%02d %02d:%02d:%02d\r\n",
                      2000 + sDate.Year, sDate.Month, sDate.Date,
                      sTime.Hours, sTime.Minutes, sTime.Seconds);
        }
        else
        {
            my_printf(&huart1, "Error: Failed to get RTC date\r\n");
        }
    }
    else
    {
        my_printf(&huart1, "Error: Failed to get RTC time\r\n");
    }
}
