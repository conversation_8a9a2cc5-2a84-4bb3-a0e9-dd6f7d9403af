#ifndef __CONFIG_MANAGER_H__
#define __CONFIG_MANAGER_H__

#include "main.h"
#include "fatfs.h"
#include "ff.h"
#include "lfs.h"

/**
 * @file    config_manager.h
 * @brief   配置管理模块头文件
 * @details 提供从TF卡读取config.ini配置文件并保存到Flash的功能
 *          支持INI文件格式解析和配置参数的持久化存储
 * <AUTHOR> Studio
 * @date    2025-01-15
 */

/**
 * @brief 配置数据结构
 * @details 存储从config.ini文件中解析出的配置参数
 */
typedef struct {
    float ratio;        // 变比参数（从[Ratio] Ch0读取）
    int limit;          // 阈值参数（从[Limit] Ch0读取）
    uint32_t version;   // 配置版本号，用于兼容性检查
} config_data_t;

/**
 * @brief 配置操作结果枚举类型
 */
typedef enum {
    CONFIG_OK = 0,                  // 操作成功
    CONFIG_FILE_NOT_FOUND = 1,      // config.ini文件不存在
    CONFIG_PARSE_ERROR = 2,         // INI文件解析错误
    CONFIG_FLASH_ERROR = 3          // Flash存储操作失败
} config_result_t;

/**
 * @brief 从TF卡读取配置文件并保存到Flash
 * @details 从TF卡根目录读取config.ini文件，解析[Ratio]和[Limit]段的Ch0参数，
 *          并将解析结果保存到Flash存储中。支持多种INI文件格式和容错处理。
 * @param  None
 * @retval config_result_t 操作结果（CONFIG_OK: 成功, 其他: 失败）
 */
config_result_t config_read_from_tfcard(void);

/**
 * @brief 从Flash加载配置数据
 * @details 从Flash存储中读取之前保存的配置参数，如果文件不存在则使用默认值
 * @param  data 输出参数，存储读取到的配置数据
 * @retval config_result_t 操作结果（CONFIG_OK: 成功, 其他: 失败）
 */
config_result_t config_load_from_flash(config_data_t* data);

/**
 * @brief 保存配置数据到Flash
 * @details 将配置参数保存到Flash存储的/config/system.cfg文件中
 * @param  data 要保存的配置数据
 * @retval config_result_t 操作结果（CONFIG_OK: 成功, 其他: 失败）
 */
config_result_t config_save_to_flash(const config_data_t* data);

/**
 * @brief 获取当前配置的Ratio值
 * @details 从Flash中读取当前的变比配置值
 * @param  None
 * @retval float 当前的Ratio值，失败时返回默认值
 */
float config_get_ratio(void);

/**
 * @brief 获取当前配置的Limit值
 * @details 从Flash中读取当前的阈值配置值
 * @param  None
 * @retval int 当前的Limit值，失败时返回默认值
 */
int config_get_limit(void);

#endif // __CONFIG_MANAGER_H__
