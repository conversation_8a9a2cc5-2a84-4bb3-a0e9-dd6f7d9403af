#ifndef __RTC_CONFIG_H__
#define __RTC_CONFIG_H__

#include "mydefine.h"

/**
 * @file    rtc_config.h
 * @brief   RTC时间配置模块头文件
 * @details 提供RTC时间设置和显示功能的接口定义
 *          支持灵活的时间格式解析和串口命令处理
 * <AUTHOR> Studio
 * @date    2025-01-15
 */

/**
 * @brief RTC配置结果枚举类型
 */
typedef enum {
    RTC_CONFIG_OK = 0,           // 配置成功
    RTC_CONFIG_PARSE_ERROR = 1,  // 时间格式解析错误
    RTC_CONFIG_SET_ERROR = 2     // RTC设置失败
} rtc_config_result_t;

/**
 * @brief RTC时间设置函数
 * @details 解析时间字符串并设置RTC时间和日期
 *          支持多种时间格式：YYYY MM DD HH MM SS（分隔符可变）
 *          支持的分隔符：空格、-、:、/等
 * @param  time_str 时间字符串，格式如"2025 01 01 12 00 30"或"2025-01-01 12:00:30"
 * @retval rtc_config_result_t 设置结果（RTC_CONFIG_OK: 成功, 其他: 失败）
 */
rtc_config_result_t rtc_config_set_time(const char* time_str);

/**
 * @brief RTC当前时间显示函数
 * @details 获取并显示当前RTC时间，格式为：Current Time: YYYY-MM-DD HH:MM:SS
 *          使用HAL库接口获取时间和日期信息，通过串口输出
 * @param  None
 * @retval None
 */
void rtc_current_time_display(void);

#endif // __RTC_CONFIG_H__
